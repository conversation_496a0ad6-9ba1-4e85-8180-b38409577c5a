<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once dirname(__DIR__) . '/config/database.php';

$current_admin = getCurrentAdmin();

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $order_id = (int)$_POST['order_id'];
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes']);

    try {
        $pdo = getDBConnection();

        // Get current status
        $stmt = $pdo->prepare("SELECT status FROM orders WHERE id = ?");
        $stmt->execute([$order_id]);
        $current_status = $stmt->fetchColumn();

        // Update order status
        $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$new_status, $order_id]);

        // Add to history
        $stmt = $pdo->prepare("INSERT INTO order_history (order_id, status_from, status_to, notes, changed_by) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$order_id, $current_status, $new_status, $notes, $current_admin['id']]);

        $success_message = 'Order status updated successfully.';
    } catch (PDOException $e) {
        $error_message = 'Error updating order status.';
    }
}

// Handle launch notification
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_launch_notification'])) {
    $order_id = (int)$_POST['order_id'];

    try {
        require_once '../customer/shared/email-delivery.php';
        $result = sendLaunchNotificationEmail($order_id);

        if ($result['success']) {
            $success_message = 'Launch notification sent successfully.';
        } else {
            $error_message = 'Failed to send launch notification: ' . $result['error'];
        }

    } catch (Exception $e) {
        $error_message = 'Failed to send launch notification: ' . $e->getMessage();
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';
$search_query = trim($_GET['search'] ?? '');

// Get pagination parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page_options = [10, 25, 50, 100];
$per_page = (int)($_GET['per_page'] ?? 10);
if (!in_array($per_page, $per_page_options)) {
    $per_page = 10; // Default fallback
}
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "o.billboard_type = ?";
    $params[] = $type_filter;
}

if ($search_query) {
    $where_conditions[] = "(o.order_number LIKE ? OR o.customer_name LIKE ?)";
    $search_param = '%' . $search_query . '%';
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $pdo = getDBConnection();

    // Check if enhanced tables exist
    $tablesExist = true;
    try {
        $pdo->query("SELECT 1 FROM billboard_images LIMIT 1");
        $pdo->query("SELECT 1 FROM payment_records LIMIT 1");
    } catch (PDOException $e) {
        $tablesExist = false;
    }

    // Check if enhanced columns exist in orders table
    $columnsExist = true;
    try {
        $pdo->query("SELECT booking_start_date, payment_status FROM orders LIMIT 1");
    } catch (PDOException $e) {
        $columnsExist = false;
    }

    // First, get total count for pagination
    $count_query = "SELECT COUNT(*) FROM orders o $where_clause";
    $count_stmt = $pdo->prepare($count_query);
    $count_stmt->execute($params);
    $total_orders = $count_stmt->fetchColumn();
    $total_pages = ceil($total_orders / $per_page);

    if ($tablesExist && $columnsExist) {
        // Use enhanced query with new tables and columns
        $query = "
            SELECT o.*,
                   bi.image_path, bi.image_filename, bi.created_at as image_created_at,
                   pr.payment_gateway, pr.gateway_transaction_id,
                   GROUP_CONCAT(cb.booking_date ORDER BY cb.booking_date ASC SEPARATOR ', ') as booking_dates,
                   COUNT(cb.booking_date) as booking_count
            FROM orders o
            LEFT JOIN billboard_images bi ON o.id = bi.order_id
            LEFT JOIN payment_records pr ON o.id = pr.order_id
            LEFT JOIN calendar_bookings cb ON o.id = cb.order_id
            $where_clause
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT $per_page OFFSET $offset
        ";
    } else {
        // Use basic query for existing database structure
        $query = "SELECT o.* FROM orders o $where_clause ORDER BY o.created_at DESC LIMIT $per_page OFFSET $offset";
        if (!$columnsExist) {
            $error_message = 'Database schema needs to be updated. Please run the database migration script.';
        }
    }

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();

    // Get order statistics (compatible with both old and new schema)
    if ($columnsExist) {
        $stats_query = "
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN o.status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_orders,
                SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN o.status = 'paid' THEN 1 ELSE 0 END) as paid_orders,
                SUM(CASE WHEN o.status = 'launched' THEN 1 ELSE 0 END) as launched_orders,
                SUM(COALESCE(o.total_amount, 0)) as total_revenue
            FROM orders o
        ";
    } else {
        $stats_query = "
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN o.status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_orders,
                SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                0 as paid_orders,
                0 as launched_orders,
                0 as total_revenue
            FROM orders o
        ";
    }
    $stats_stmt = $pdo->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch();

} catch (PDOException $e) {
    $error_message = 'Database error occurred: ' . $e->getMessage();
    error_log("Database error in orders.php: " . $e->getMessage());
    $orders = [];
    $stats = [];
    $total_orders = 0;
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <div class="header-brand">
                    <img src="../assets/images/bm-header-logo.png" alt="Borges Media Logo" class="admin-logo">
                    <h1>Borges Media Billboard Admin</h1>
                </div>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="orders.php" class="active">List of Orders</a></li>
                    <li><a href="history.php">History</a></li>
                    <li><a href="?logout=1" class="logout-btn">Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>List of Orders</h2>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-error"><?php echo $error_message; ?></div>
                <?php endif; ?>


                
                <!-- Filters -->
                <div class="filters">
                    <form method="GET" class="filter-form">
                        <div class="filter-group">
                            <label for="search">Search:</label>
                            <input type="text" name="search" id="search" placeholder="Order # or Customer name..." value="<?php echo htmlspecialchars($search_query); ?>">
                        </div>

                        <!-- <div class="filter-group">
                            <label for="status">Status:</label>
                            <select name="status" id="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                <option value="launched" <?php echo $status_filter === 'launched' ? 'selected' : ''; ?>>Launched</option>
                            </select>
                        </div> -->

                        <div class="filter-group">
                            <label for="type">Type:</label>
                            <select name="type" id="type">
                                <option value="">All Types</option>
                                <option value="templated" <?php echo $type_filter === 'templated' ? 'selected' : ''; ?>>Templated</option>
                                <option value="custom" <?php echo $type_filter === 'custom' ? 'selected' : ''; ?>>Custom</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="per_page">Show:</label>
                            <select name="per_page" id="per_page">
                                <?php foreach ($per_page_options as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php echo $per_page === $option ? 'selected' : ''; ?>>
                                        <?php echo $option; ?> rows
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="orders.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </form>
                </div>
                
                <!-- Orders Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Type</th>
                                <th>Calendar Bookings</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($orders)): ?>
                                <?php foreach ($orders as $order): ?>
                                    <tr data-order-id="<?php echo $order['id']; ?>"
                                        data-order-number="<?php echo htmlspecialchars($order['order_number']); ?>"
                                        data-customer-name="<?php echo htmlspecialchars($order['customer_name']); ?>"
                                        data-image-path="<?php echo htmlspecialchars($order['image_path'] ?? $order['billboard_image_path'] ?? ''); ?>"
                                        data-booking-dates="<?php echo htmlspecialchars($order['booking_dates'] ?? ''); ?>"
                                        data-booking-count="<?php echo $order['booking_count'] ?? 0; ?>">
                                        <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_email']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $order['billboard_type']; ?>">
                                                <?php echo ucfirst($order['billboard_type']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($order['booking_dates'])): ?>
                                                <div class="booking-dates">
                                                    <span class="booking-count"><?php echo $order['booking_count']; ?> days</span>
                                                    <div class="booking-dates-list">
                                                        <?php
                                                        $dates = explode(', ', $order['booking_dates']);
                                                        $displayDates = array_slice($dates, 0, 3); // Show first 3 dates
                                                        foreach ($displayDates as $date):
                                                        ?>
                                                            <span class="booking-date"><?php echo date('M j', strtotime($date)); ?></span>
                                                        <?php endforeach; ?>
                                                        <?php if (count($dates) > 3): ?>
                                                            <span class="booking-more">+<?php echo count($dates) - 3; ?> more</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <span class="no-bookings">No bookings</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>$<?php echo number_format($order['total_amount'] ?? 0, 2); ?></td>
                                        <td>
                                            <button onclick="viewBillboardImage(<?php echo $order['id']; ?>)"
                                                    class="btn btn-sm" title="VIEW">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <button type="submit" name="send_launch_notification"
                                                        class="btn btn-sm" title="Send Email - LAUNCH"
                                                        onclick="return confirm('Send launch notification to customer?')">
                                                    <i class="fas fa-rocket"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">No orders found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-container">
                        <div class="pagination-info">
                            Showing <?php echo (($page - 1) * $per_page) + 1; ?> to <?php echo min($page * $per_page, $total_orders); ?> of <?php echo $total_orders; ?> orders (<?php echo $per_page; ?> per page)
                        </div>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="pagination-btn">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>

                            <?php
                            // Calculate page range to show
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            // Show first page if not in range
                            if ($start_page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>" class="pagination-btn">1</a>
                                <?php if ($start_page > 2): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="pagination-btn active"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" class="pagination-btn"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php
                            // Show last page if not in range
                            if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <span class="pagination-dots">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>" class="pagination-btn"><?php echo $total_pages; ?></a>
                            <?php endif; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="pagination-btn">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <!-- Status Update Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Update Order Status</h3>
            <form method="POST">
                <input type="hidden" id="modal_order_id" name="order_id">
                
                <div class="form-group">
                    <label for="modal_status">New Status:</label>
                    <select id="modal_status" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="paid">Paid</option>
                        <option value="launched">Launched</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="modal_notes">Notes (optional):</label>
                    <textarea id="modal_notes" name="notes" rows="3"></textarea>
                </div>
                
                <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
            </form>
        </div>
    </div>

    <!-- Billboard Image Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content modal-large">
            <span class="close" onclick="closeImageModal()">&times;</span>
            <h3 id="imageModalTitle">Billboard Image</h3>
            <div id="imageContainer">
                <img id="billboardImage" src="" alt="Billboard Image" style="max-width: 100%; height: auto;">
            </div>
            <div id="imageMetadata" class="image-metadata" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; display: none;">
                <h4>Image Details</h4>
                <div class="metadata-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                    <div><strong>Filename:</strong> <span id="metaFilename">-</span></div>
                    <div><strong>Dimensions:</strong> <span id="metaDimensions">-</span></div>
                    <div><strong>File Size:</strong> <span id="metaFileSize">-</span></div>
                    <div><strong>Format:</strong> <span id="metaFormat">-</span></div>
                    <div><strong>Generation Method:</strong> <span id="metaMethod">-</span></div>
                    <div><strong>Quality Level:</strong> <span id="metaQuality">-</span></div>
                    <div><strong>Created:</strong> <span id="metaCreated">-</span></div>
                    <div><strong>Order:</strong> <span id="metaOrder">-</span></div>
                </div>
            </div>
            <div class="image-actions">
                <a id="downloadImageLink" href="" download class="btn btn-primary">Download Image</a>
            </div>

            <!-- Calendar Bookings Section -->
            <div id="modalBookingInfo" class="modal-booking-info">
                <h4>Calendar Bookings</h4>
                <div id="modalBookingDates" class="modal-booking-dates">
                    <!-- Booking dates will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin-script.js"></script>
    <script>
        // Billboard image viewing functionality
        function viewBillboardImage(orderId) {
            // Get order data directly from the page
            const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
            if (!orderRow) {
                alert('Order not found');
                return;
            }

            const orderNumber = orderRow.dataset.orderNumber;
            const customerName = orderRow.dataset.customerName;
            const imagePath = orderRow.dataset.imagePath;
            const bookingDates = orderRow.dataset.bookingDates;
            const bookingCount = orderRow.dataset.bookingCount;

            if (!imagePath) {
                alert('No billboard image available for this order');
                return;
            }

            // Convert absolute path to web-accessible URL
            const webPath = imagePath.replace(/^.*[\\\/]uploads[\\\/]/, '/uploads/').replace(/\\/g, '/');

            // Set modal title with customer name and order number
            document.getElementById('imageModalTitle').textContent = `Billboard Image - ${orderNumber} (${customerName})`;

            // Set image
            document.getElementById('billboardImage').src = webPath;
            document.getElementById('downloadImageLink').href = webPath;

            // Set basic metadata
            document.getElementById('metaFilename').textContent = imagePath.split(/[\\\/]/).pop() || '-';
            document.getElementById('metaDimensions').textContent = '-';
            document.getElementById('metaFileSize').textContent = '-';
            document.getElementById('metaFormat').textContent = 'PNG';
            document.getElementById('metaMethod').textContent = 'Generated';
            document.getElementById('metaQuality').textContent = 'High';
            document.getElementById('metaCreated').textContent = '-';
            document.getElementById('metaOrder').textContent = `${orderNumber} (${customerName})`;

            // Reset metadata visibility
            document.getElementById('imageMetadata').style.display = 'none';

            // Populate booking information
            const modalBookingDates = document.getElementById('modalBookingDates');
            if (bookingDates && bookingDates.trim() !== '') {
                const dates = bookingDates.split(', ');
                let bookingHTML = `<div class="modal-booking-summary">
                    <span class="booking-count-large">${bookingCount} days booked</span>
                </div>
                <div class="modal-booking-dates-grid">`;

                // Show only start and end dates, or single date if only one day
                if (dates.length === 1) {
                    // Single date
                    const formattedDate = new Date(dates[0]).toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    });
                    bookingHTML += `<span class="modal-booking-date">${formattedDate}</span>`;
                } else if (dates.length > 1) {
                    // Multiple dates - show start and end only
                    const startDate = new Date(dates[0]).toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    });
                    const endDate = new Date(dates[dates.length - 1]).toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    });
                    bookingHTML += `<span class="modal-booking-date">${startDate}</span>`;
                    if (dates.length > 1) {
                        bookingHTML += `<span class="modal-booking-separator"> to </span>`;
                        bookingHTML += `<span class="modal-booking-date">${endDate}</span>`;
                    }
                }

                bookingHTML += '</div>';
                modalBookingDates.innerHTML = bookingHTML;
            } else {
                modalBookingDates.innerHTML = '<div class="no-bookings-modal">No calendar bookings for this order</div>';
            }

            document.getElementById('imageModal').style.display = 'block';
        }

        function formatGenerationMethod(method) {
            const methods = {
                'fabric_high_quality': 'Fabric.js High Quality',
                'fabric_checkout_optimized': 'Fabric.js Checkout',
                'fabric_captured_image': 'Fabric.js Captured',
                'fabric_standard_fallback': 'Fabric.js Standard',
                'fabric_direct_fallback': 'Fabric.js Direct',
                'high_quality': 'High Quality Generator',
                'basic': 'Basic Generator',
                'emergency_placeholder': 'Emergency Placeholder'
            };
            return methods[method] || (method || 'Unknown').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function formatQualityLevel(level) {
            const levels = {
                'highQuality': 'Ultra High (4x)',
                'standardQuality': 'High (2x)',
                'fallbackQuality': 'Standard (1x)',
                'high': 'High Quality',
                'standard': 'Standard Quality',
                'basic': 'Basic Quality'
            };
            return levels[level] || (level || 'Unknown').replace(/([A-Z])/g, ' $1').trim();
        }

        function toggleImageMetadata() {
            const metadata = document.getElementById('imageMetadata');
            const button = document.querySelector('.image-actions button');

            if (metadata.style.display === 'none') {
                metadata.style.display = 'block';
                button.textContent = 'Hide Details';
            } else {
                metadata.style.display = 'none';
                button.textContent = 'Show Details';
            }
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const imageModal = document.getElementById('imageModal');
            if (event.target === imageModal) {
                closeImageModal();
            }
        }
    </script>
</body>
</html>
